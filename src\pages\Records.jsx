import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { getRecords, getCategories, deleteRecord } from '../utils/storage'

function Records() {
  const [records, setRecords] = useState([])
  const [categories, setCategories] = useState({ income: [], expense: [] })
  const [filteredRecords, setFilteredRecords] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all') // all, income, expense
  const [filterCategory, setFilterCategory] = useState('all')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    loadData()
    
    // 监听存储变化
    const handleStorageChange = () => {
      loadData()
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  useEffect(() => {
    // 应用筛选和搜索
    let filtered = records

    // 类型筛选
    if (filterType !== 'all') {
      filtered = filtered.filter(record => record.type === filterType)
    }

    // 分类筛选
    if (filterCategory !== 'all') {
      filtered = filtered.filter(record => record.category === filterCategory)
    }

    // 搜索筛选
    if (searchTerm) {
      filtered = filtered.filter(record =>
        record.note?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        getCategoryName(record.category, record.type).toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredRecords(filtered)
  }, [records, searchTerm, filterType, filterCategory])

  const loadData = () => {
    const recordsData = getRecords()
    const categoriesData = getCategories()
    setRecords(recordsData)
    setCategories(categoriesData)
  }

  const getCategoryName = (categoryId, type) => {
    const categoryList = categories[type] || []
    const category = categoryList.find(cat => cat.id === categoryId)
    return category ? category.name : '未知分类'
  }

  const getCategoryIcon = (categoryId, type) => {
    const categoryList = categories[type] || []
    const category = categoryList.find(cat => cat.id === categoryId)
    return category ? category.icon : '📝'
  }

  const handleDelete = (id) => {
    if (window.confirm('确定要删除这条记录吗？')) {
      deleteRecord(id)
      loadData()
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return '今天'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天'
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      })
    }
  }

  // 按日期分组记录
  const groupedRecords = Array.isArray(filteredRecords) ? filteredRecords.reduce((groups, record) => {
    const date = record.date
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(record)
    return groups
  }, {}) : {}

  const allCategories = [...categories.income, ...categories.expense]

  return (
    <div className="space-y-4">
      {/* 搜索和筛选 */}
      <div className="card">
        <div className="flex gap-2 mb-3">
          <div className="flex-1 relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" style={{ fontSize: '20px' }}>🔍</span>
            <input
              type="text"
              placeholder="搜索记录..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10"
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn ${showFilters ? 'btn-primary' : 'btn-secondary'}`}
          >
            <span style={{ fontSize: '20px' }}>🔽</span>
          </button>
        </div>

        {showFilters && (
          <div className="space-y-3 pt-3 border-t border-gray-100">
            <div>
              <label className="block text-sm font-medium mb-2">类型</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="input"
              >
                <option value="all">全部</option>
                <option value="income">收入</option>
                <option value="expense">支出</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">分类</label>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="input"
              >
                <option value="all">全部分类</option>
                {allCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* 记录列表 */}
      {Object.keys(groupedRecords).length === 0 ? (
        <div className="card text-center py-8">
          <p className="text-gray-500 mb-4">没有找到记录</p>
          <Link to="/add" className="btn btn-primary">
            <span className="mr-2" style={{ fontSize: '20px' }}>➕</span>
            添加记录
          </Link>
        </div>
      ) : (
        Object.entries(groupedRecords)
          .sort(([a], [b]) => new Date(b) - new Date(a))
          .map(([date, dayRecords]) => (
            <div key={date} className="card">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-700">
                  {formatDate(date)}
                </h3>
                <span className="text-sm text-gray-500">{date}</span>
              </div>
              
              <div className="space-y-2">
                {dayRecords.map((record) => (
                  <div key={record.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">
                        {getCategoryIcon(record.category, record.type)}
                      </div>
                      <div>
                        <div className="font-medium">
                          {getCategoryName(record.category, record.type)}
                        </div>
                        {record.note && (
                          <div className="text-sm text-gray-500">{record.note}</div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className={`font-semibold ${record.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                        {record.type === 'income' ? '+' : '-'}{formatCurrency(Math.abs(record.amount))}
                      </div>
                      <button
                        onClick={() => handleDelete(record.id)}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <span style={{ fontSize: '16px' }}>🗑️</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
      )}

      {/* 浮动添加按钮 */}
      <Link
        to="/add"
        className="fixed bottom-20 right-4 w-14 h-14 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors"
      >
        <span style={{ fontSize: '24px' }}>➕</span>
      </Link>
    </div>
  )
}

export default Records
