// 本地存储管理工具

// 存储键名
const STORAGE_KEYS = {
  RECORDS: 'money_app_records',
  CATEGORIES: 'money_app_categories',
  SETTINGS: 'money_app_settings'
}

// 默认分类数据
const DEFAULT_CATEGORIES = {
  expense: [
    { id: 'food', name: '餐饮', icon: '🍽️' },
    { id: 'transport', name: '交通', icon: '🚗' },
    { id: 'shopping', name: '购物', icon: '🛍️' },
    { id: 'entertainment', name: '娱乐', icon: '🎮' },
    { id: 'medical', name: '医疗', icon: '🏥' },
    { id: 'education', name: '教育', icon: '📚' },
    { id: 'housing', name: '住房', icon: '🏠' },
    { id: 'utilities', name: '水电', icon: '💡' },
    { id: 'other', name: '其他', icon: '📝' }
  ],
  income: [
    { id: 'salary', name: '工资', icon: '💰' },
    { id: 'bonus', name: '奖金', icon: '🎁' },
    { id: 'investment', name: '投资', icon: '📈' },
    { id: 'freelance', name: '兼职', icon: '💼' },
    { id: 'gift', name: '礼金', icon: '🧧' },
    { id: 'other', name: '其他', icon: '📝' }
  ]
}

// 默认设置
const DEFAULT_SETTINGS = {
  currency: 'CNY',
  theme: 'light',
  notifications: true,
  autoBackup: false
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取当前日期字符串
function getCurrentDate() {
  return new Date().toISOString().split('T')[0]
}

// 获取当前月份字符串 (YYYY-MM)
function getCurrentMonth() {
  return new Date().toISOString().slice(0, 7)
}

// 安全的JSON解析
function safeJsonParse(str, defaultValue = null) {
  try {
    return JSON.parse(str)
  } catch (error) {
    console.error('JSON解析错误:', error)
    return defaultValue
  }
}

// 记录相关操作
export function getRecords() {
  const records = localStorage.getItem(STORAGE_KEYS.RECORDS)
  const parsed = safeJsonParse(records, [])
  // 确保返回的是数组
  return Array.isArray(parsed) ? parsed : []
}

export function saveRecord(record) {
  const records = getRecords()
  const newRecord = {
    id: generateId(),
    ...record,
    createdAt: new Date().toISOString(),
    date: record.date || getCurrentDate()
  }
  records.unshift(newRecord) // 添加到开头
  localStorage.setItem(STORAGE_KEYS.RECORDS, JSON.stringify(records))
  return newRecord
}

export function updateRecord(id, updates) {
  const records = getRecords()
  const index = records.findIndex(record => record.id === id)
  if (index !== -1) {
    records[index] = { ...records[index], ...updates, updatedAt: new Date().toISOString() }
    localStorage.setItem(STORAGE_KEYS.RECORDS, JSON.stringify(records))
    return records[index]
  }
  return null
}

export function deleteRecord(id) {
  const records = getRecords()
  const filteredRecords = records.filter(record => record.id !== id)
  localStorage.setItem(STORAGE_KEYS.RECORDS, JSON.stringify(filteredRecords))
  return true
}

// 分类相关操作
export function getCategories() {
  const categories = localStorage.getItem(STORAGE_KEYS.CATEGORIES)
  const parsed = safeJsonParse(categories, null)
  if (!parsed) {
    // 首次使用，初始化默认分类
    localStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(DEFAULT_CATEGORIES))
    return DEFAULT_CATEGORIES
  }
  return parsed
}

export function saveCategory(type, category) {
  const categories = getCategories()
  const newCategory = {
    id: generateId(),
    ...category
  }
  categories[type].push(newCategory)
  localStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(categories))
  return newCategory
}

// 统计相关操作
export function getTotalBalance() {
  const records = getRecords()
  if (!Array.isArray(records) || records.length === 0) {
    return 0
  }
  return records.reduce((total, record) => {
    return record.type === 'income' ? total + record.amount : total - record.amount
  }, 0)
}

export function getMonthlyStats(month = getCurrentMonth()) {
  const records = getRecords()
  if (!Array.isArray(records) || records.length === 0) {
    return { income: 0, expense: 0 }
  }
  const monthlyRecords = records.filter(record => record.date && record.date.startsWith(month))

  return monthlyRecords.reduce((stats, record) => {
    if (record.type === 'income') {
      stats.income += record.amount
    } else {
      stats.expense += record.amount
    }
    return stats
  }, { income: 0, expense: 0 })
}

export function getCategoryStats(month = getCurrentMonth()) {
  const records = getRecords()
  const categories = getCategories()

  const stats = {
    income: {},
    expense: {}
  }

  // 初始化分类统计
  if (categories.income && Array.isArray(categories.income)) {
    categories.income.forEach(cat => {
      stats.income[cat.id] = { ...cat, amount: 0, count: 0 }
    })
  }
  if (categories.expense && Array.isArray(categories.expense)) {
    categories.expense.forEach(cat => {
      stats.expense[cat.id] = { ...cat, amount: 0, count: 0 }
    })
  }

  // 计算统计数据
  if (Array.isArray(records) && records.length > 0) {
    const monthlyRecords = records.filter(record => record.date && record.date.startsWith(month))
    monthlyRecords.forEach(record => {
      if (stats[record.type] && stats[record.type][record.category]) {
        stats[record.type][record.category].amount += record.amount
        stats[record.type][record.category].count += 1
      }
    })
  }

  return stats
}

// 设置相关操作
export function getSettings() {
  const settings = localStorage.getItem(STORAGE_KEYS.SETTINGS)
  const parsed = safeJsonParse(settings, null)
  if (!parsed) {
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(DEFAULT_SETTINGS))
    return DEFAULT_SETTINGS
  }
  return parsed
}

export function updateSettings(updates) {
  const settings = getSettings()
  const newSettings = { ...settings, ...updates }
  localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(newSettings))
  return newSettings
}

// 数据导出和导入
export function exportData() {
  return {
    records: getRecords(),
    categories: getCategories(),
    settings: getSettings(),
    exportDate: new Date().toISOString()
  }
}

export function importData(data) {
  try {
    if (data.records) {
      localStorage.setItem(STORAGE_KEYS.RECORDS, JSON.stringify(data.records))
    }
    if (data.categories) {
      localStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(data.categories))
    }
    if (data.settings) {
      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings))
    }
    return true
  } catch (error) {
    console.error('数据导入失败:', error)
    return false
  }
}

// 清空所有数据
export function clearAllData() {
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key)
  })
}
